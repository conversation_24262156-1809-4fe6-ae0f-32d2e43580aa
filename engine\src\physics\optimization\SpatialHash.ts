/**
 * 空间哈希分区
 * 用于加速碰撞检测
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { ISpatialPartitioning } from './SpatialPartitioning';
import { Debug } from '../../utils/Debug';

/**
 * 空间哈希配置接口
 */
export interface SpatialHashOptions {
  /** 单元大小 */
  cellSize?: number;
  /** 世界大小 */
  worldSize?: number;
  /** 世界中心 */
  worldCenter?: CANNON.Vec3;
  /** 是否使用动态哈希 */
  useDynamicHash?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 空间哈希类
 */
export class SpatialHash implements ISpatialPartitioning {
  /** 单元大小 */
  private cellSize: number;

  /** 世界大小 */
  private worldSize: number;

  /** 世界中心 */
  private worldCenter: CANNON.Vec3;

  /** 是否使用动态哈希 */
  private useDynamicHash: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 哈希表 */
  private hashTable: Map<number, Set<CANNON.Body>>;

  /** 物体到哈希的映射 */
  private bodyToHashes: Map<CANNON.Body, Set<number>>;

  /** 物体列表 */
  private bodies: Set<CANNON.Body>;

  /** 调试网格 */
  private debugMesh: THREE.Object3D | null = null;

  /**
   * 创建空间哈希
   * @param options 空间哈希配置
   */
  constructor(options: SpatialHashOptions = {}) {
    this.cellSize = options.cellSize || 5;
    this.worldSize = options.worldSize || 100;
    this.worldCenter = options.worldCenter || new CANNON.Vec3(0, 0, 0);
    this.useDynamicHash = options.useDynamicHash !== undefined ? options.useDynamicHash : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    this.hashTable = new Map();
    this.bodyToHashes = new Map();
    this.bodies = new Set();

    if (this.useDebugVisualization) {
      this.createDebugMesh();
    }
  }

  /**
   * 创建调试网格
   */
  private createDebugMesh(): void {
    this.debugMesh = new THREE.Object3D();
    this.debugMesh.name = 'spatial_hash_debug';

    // 创建网格线
    const gridHelper = new THREE.GridHelper(this.worldSize, Math.floor(this.worldSize / this.cellSize));
    (gridHelper as any).setPosition(0, 0, 0);
    this.debugMesh.add(gridHelper);
  }

  /**
   * 计算哈希值
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 哈希值
   */
  private computeHash(x: number, y: number, z: number): number {
    // 使用空间填充曲线（Z-order曲线）计算哈希值
    // 这种方法可以保持空间局部性，使得相邻的空间点映射到相邻的哈希值
    const ix = Math.floor(x / this.cellSize);
    const iy = Math.floor(y / this.cellSize);
    const iz = Math.floor(z / this.cellSize);

    // 使用位运算计算哈希值
    // 这里使用了一个简单的空间填充曲线算法
    // 在实际项目中，可以使用更复杂的算法，如Morton编码
    return (ix * 73856093) ^ (iy * 19349663) ^ (iz * 83492791);
  }

  /**
   * 获取物体占据的哈希值
   * @param body 物体
   * @returns 哈希值列表
   */
  private getBodyHashes(body: CANNON.Body): number[] {
    // 获取物体的AABB
    const aabb = this.getBodyAABB(body);
    const min = aabb.lowerBound;
    const max = aabb.upperBound;

    // 计算AABB占据的单元范围
    const minCellX = Math.floor(min.x / this.cellSize);
    const minCellY = Math.floor(min.y / this.cellSize);
    const minCellZ = Math.floor(min.z / this.cellSize);
    const maxCellX = Math.floor(max.x / this.cellSize);
    const maxCellY = Math.floor(max.y / this.cellSize);
    const maxCellZ = Math.floor(max.z / this.cellSize);

    // 收集物体占据的所有哈希值
    const hashes: number[] = [];
    for (let x = minCellX; x <= maxCellX; x++) {
      for (let y = minCellY; y <= maxCellY; y++) {
        for (let z = minCellZ; z <= maxCellZ; z++) {
          const hash = this.computeHash(x * this.cellSize, y * this.cellSize, z * this.cellSize);
          hashes.push(hash);
        }
      }
    }

    return hashes;
  }

  /**
   * 获取物体的AABB
   * @param body 物体
   * @returns AABB
   */
  private getBodyAABB(body: CANNON.Body): CANNON.AABB {
    const aabb = new CANNON.AABB();
    // 计算物体的AABB
    if (body.shapes.length > 0) {
      // 使用 calculateWorldAABB 方法计算世界空间的AABB
      body.shapes[0].calculateWorldAABB(body.position, body.quaternion, aabb.lowerBound, aabb.upperBound);
    } else {
      // 如果没有形状，使用物体位置作为点AABB
      aabb.lowerBound.copy(body.position);
      aabb.upperBound.copy(body.position);
    }
    return aabb;
  }

  /**
   * 添加物体
   * @param body 物体
   */
  public add(body: CANNON.Body): void {
    // 如果物体已经存在，则先移除
    if (this.bodies.has(body)) {
      this.remove(body);
    }

    // 添加物体
    this.bodies.add(body);

    // 获取物体占据的哈希值
    const hashes = this.getBodyHashes(body);
    const hashSet = new Set<number>(hashes);
    this.bodyToHashes.set(body, hashSet);

    // 将物体添加到哈希表
    for (const hash of hashes) {
      let cell = this.hashTable.get(hash);
      if (!cell) {
        cell = new Set<CANNON.Body>();
        this.hashTable.set(hash, cell);
      }
      cell.add(body);
    }
  }

  /**
   * 移除物体
   * @param body 物体
   */
  public remove(body: CANNON.Body): void {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return;
    }

    // 移除物体
    this.bodies.delete(body);

    // 获取物体占据的哈希值
    const hashes = this.bodyToHashes.get(body);
    if (hashes) {
      // 从哈希表中移除物体
      for (const hash of hashes) {
        const cell = this.hashTable.get(hash);
        if (cell) {
          cell.delete(body);
          // 如果单元为空，则移除单元
          if (cell.size === 0) {
            this.hashTable.delete(hash);
          }
        }
      }
    }

    // 移除物体到哈希的映射
    this.bodyToHashes.delete(body);
  }

  /**
   * 更新物体
   * @param body 物体
   */
  public update(body: CANNON.Body): void {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return;
    }

    // 获取物体当前占据的哈希值
    const oldHashes = this.bodyToHashes.get(body);
    if (!oldHashes) {
      return;
    }

    // 获取物体新占据的哈希值
    const newHashes = this.getBodyHashes(body);
    const newHashSet = new Set<number>(newHashes);

    // 如果哈希值没有变化，则直接返回
    if (this.areHashesEqual(oldHashes, newHashSet)) {
      return;
    }

    // 从旧哈希值中移除物体
    for (const hash of oldHashes) {
      // 如果新哈希值中也包含该哈希值，则跳过
      if (newHashSet.has(hash)) {
        continue;
      }

      // 从哈希表中移除物体
      const cell = this.hashTable.get(hash);
      if (cell) {
        cell.delete(body);
        // 如果单元为空，则移除单元
        if (cell.size === 0) {
          this.hashTable.delete(hash);
        }
      }
    }

    // 将物体添加到新哈希值
    for (const hash of newHashSet) {
      // 如果旧哈希值中也包含该哈希值，则跳过
      if (oldHashes.has(hash)) {
        continue;
      }

      // 将物体添加到哈希表
      let cell = this.hashTable.get(hash);
      if (!cell) {
        cell = new Set<CANNON.Body>();
        this.hashTable.set(hash, cell);
      }
      cell.add(body);
    }

    // 更新物体到哈希的映射
    this.bodyToHashes.set(body, newHashSet);
  }

  /**
   * 比较两个哈希值集合是否相等
   * @param a 哈希值集合A
   * @param b 哈希值集合B
   * @returns 是否相等
   */
  private areHashesEqual(a: Set<number>, b: Set<number>): boolean {
    if (a.size !== b.size) {
      return false;
    }

    for (const hash of a) {
      if (!b.has(hash)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 更新所有物体
   */
  public updateAll(): void {
    for (const body of this.bodies) {
      this.update(body);
    }
  }

  /**
   * 查询区域内的物体
   * @param min 最小坐标
   * @param max 最大坐标
   * @returns 区域内的物体
   */
  public queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[] {
    // 计算区域占据的单元范围
    const minCellX = Math.floor(min.x / this.cellSize);
    const minCellY = Math.floor(min.y / this.cellSize);
    const minCellZ = Math.floor(min.z / this.cellSize);
    const maxCellX = Math.floor(max.x / this.cellSize);
    const maxCellY = Math.floor(max.y / this.cellSize);
    const maxCellZ = Math.floor(max.z / this.cellSize);

    // 收集区域内的所有物体
    const bodies = new Set<CANNON.Body>();
    for (let x = minCellX; x <= maxCellX; x++) {
      for (let y = minCellY; y <= maxCellY; y++) {
        for (let z = minCellZ; z <= maxCellZ; z++) {
          const hash = this.computeHash(x * this.cellSize, y * this.cellSize, z * this.cellSize);
          const cell = this.hashTable.get(hash);
          if (cell) {
            for (const body of cell) {
              bodies.add(body);
            }
          }
        }
      }
    }

    // 过滤出真正在区域内的物体
    return Array.from(bodies).filter(body => {
      const aabb = this.getBodyAABB(body);
      return this.aabbOverlap(aabb.lowerBound, aabb.upperBound, min, max);
    });
  }

  /**
   * 检查两个AABB是否重叠
   * @param minA AABB A的最小坐标
   * @param maxA AABB A的最大坐标
   * @param minB AABB B的最小坐标
   * @param maxB AABB B的最大坐标
   * @returns 是否重叠
   */
  private aabbOverlap(minA: CANNON.Vec3, maxA: CANNON.Vec3, minB: CANNON.Vec3, maxB: CANNON.Vec3): boolean {
    return (
      maxA.x >= minB.x && minA.x <= maxB.x &&
      maxA.y >= minB.y && minA.y <= maxB.y &&
      maxA.z >= minB.z && minA.z <= maxB.z
    );
  }

  /**
   * 查询射线碰撞的物体
   * @param from 射线起点
   * @param to 射线终点
   * @returns 射线碰撞的物体
   */
  public queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[] {
    // 创建射线
    const direction = new CANNON.Vec3();
    direction.copy(to);
    direction.vsub(from, direction);
    const length = direction.length();
    direction.normalize();

    // 使用DDA算法遍历射线经过的单元
    const bodies = new Set<CANNON.Body>();
    this.traverseRay(from, direction, length, (hash) => {
      const cell = this.hashTable.get(hash);
      if (cell) {
        for (const body of cell) {
          bodies.add(body);
        }
      }
    });

    // 过滤出真正与射线碰撞的物体
    return Array.from(bodies).filter(body => {
      // 在实际项目中，这里应该使用更精确的射线-物体碰撞检测
      // 这里使用简化的实现
      const aabb = this.getBodyAABB(body);
      return this.rayAabbIntersect(from, direction, length, aabb.lowerBound, aabb.upperBound);
    });
  }

  /**
   * 遍历射线经过的单元
   * @param from 射线起点
   * @param direction 射线方向
   * @param length 射线长度
   * @param callback 回调函数
   */
  private traverseRay(from: CANNON.Vec3, direction: CANNON.Vec3, length: number, callback: (hash: number) => void): void {
    // 获取起点所在的单元
    const startCellX = Math.floor(from.x / this.cellSize);
    const startCellY = Math.floor(from.y / this.cellSize);
    const startCellZ = Math.floor(from.z / this.cellSize);
    let x = startCellX;
    let y = startCellY;
    let z = startCellZ;

    // 调用起点单元的回调
    const startHash = this.computeHash(x * this.cellSize, y * this.cellSize, z * this.cellSize);
    callback(startHash);

    // 计算射线与单元边界的交点
    const tDeltaX = Math.abs(direction.x) < 1e-6 ? 1e6 : Math.abs(this.cellSize / direction.x);
    const tDeltaY = Math.abs(direction.y) < 1e-6 ? 1e6 : Math.abs(this.cellSize / direction.y);
    const tDeltaZ = Math.abs(direction.z) < 1e-6 ? 1e6 : Math.abs(this.cellSize / direction.z);

    // 计算射线到下一个单元边界的距离
    const cellX = (x * this.cellSize);
    const cellY = (y * this.cellSize);
    const cellZ = (z * this.cellSize);

    let tMaxX = direction.x > 0 ?
      ((cellX + this.cellSize) - from.x) / direction.x :
      (cellX - from.x) / direction.x;
    let tMaxY = direction.y > 0 ?
      ((cellY + this.cellSize) - from.y) / direction.y :
      (cellY - from.y) / direction.y;
    let tMaxZ = direction.z > 0 ?
      ((cellZ + this.cellSize) - from.z) / direction.z :
      (cellZ - from.z) / direction.z;

    // 计算步进方向
    const stepX = direction.x > 0 ? 1 : -1;
    const stepY = direction.y > 0 ? 1 : -1;
    const stepZ = direction.z > 0 ? 1 : -1;

    // 遍历射线经过的单元
    let t = 0;
    while (t < length) {
      // 找到最近的交点
      if (tMaxX < tMaxY && tMaxX < tMaxZ) {
        t = tMaxX;
        tMaxX += tDeltaX;
        x += stepX;
      } else if (tMaxY < tMaxZ) {
        t = tMaxY;
        tMaxY += tDeltaY;
        y += stepY;
      } else {
        t = tMaxZ;
        tMaxZ += tDeltaZ;
        z += stepZ;
      }

      // 如果超出射线长度，则停止
      if (t > length) {
        break;
      }

      // 调用单元的回调
      const hash = this.computeHash(x * this.cellSize, y * this.cellSize, z * this.cellSize);
      callback(hash);
    }
  }

  /**
   * 检查射线是否与AABB相交
   * @param from 射线起点
   * @param direction 射线方向
   * @param length 射线长度
   * @param min AABB的最小坐标
   * @param max AABB的最大坐标
   * @returns 是否相交
   */
  private rayAabbIntersect(from: CANNON.Vec3, direction: CANNON.Vec3, length: number, min: CANNON.Vec3, max: CANNON.Vec3): boolean {
    // 计算射线与AABB各面的交点
    const tmin = new CANNON.Vec3();
    const tmax = new CANNON.Vec3();

    tmin.x = (min.x - from.x) / direction.x;
    tmax.x = (max.x - from.x) / direction.x;
    if (tmin.x > tmax.x) {
      const temp = tmin.x;
      tmin.x = tmax.x;
      tmax.x = temp;
    }

    tmin.y = (min.y - from.y) / direction.y;
    tmax.y = (max.y - from.y) / direction.y;
    if (tmin.y > tmax.y) {
      const temp = tmin.y;
      tmin.y = tmax.y;
      tmax.y = temp;
    }

    tmin.z = (min.z - from.z) / direction.z;
    tmax.z = (max.z - from.z) / direction.z;
    if (tmin.z > tmax.z) {
      const temp = tmin.z;
      tmin.z = tmax.z;
      tmax.z = temp;
    }

    // 计算交点范围
    const tNear = Math.max(tmin.x, Math.max(tmin.y, tmin.z));
    const tFar = Math.min(tmax.x, Math.min(tmax.y, tmax.z));

    // 如果最近的交点比最远的交点还远，或者最远的交点为负，则没有相交
    if (tNear > tFar || tFar < 0) {
      return false;
    }

    // 如果最近的交点超出射线长度，则没有相交
    if (tNear > length) {
      return false;
    }

    return true;
  }

  /**
   * 查询球体碰撞的物体
   * @param center 球体中心
   * @param radius 球体半径
   * @returns 球体碰撞的物体
   */
  public querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[] {
    // 创建包围球体的AABB
    const min = new CANNON.Vec3(center.x - radius, center.y - radius, center.z - radius);
    const max = new CANNON.Vec3(center.x + radius, center.y + radius, center.z + radius);

    // 查询AABB内的物体
    const bodies = this.queryRegion(min, max);

    // 过滤出真正与球体碰撞的物体
    return bodies.filter(body => {
      // 在实际项目中，这里应该使用更精确的球体-物体碰撞检测
      // 这里使用简化的实现
      const aabb = this.getBodyAABB(body);
      return this.sphereAabbIntersect(center, radius, aabb.lowerBound, aabb.upperBound);
    });
  }

  /**
   * 检查球体是否与AABB相交
   * @param center 球体中心
   * @param radius 球体半径
   * @param min AABB的最小坐标
   * @param max AABB的最大坐标
   * @returns 是否相交
   */
  private sphereAabbIntersect(center: CANNON.Vec3, radius: number, min: CANNON.Vec3, max: CANNON.Vec3): boolean {
    // 计算球体中心到AABB的最近点
    const closestPoint = new CANNON.Vec3();
    closestPoint.x = Math.max(min.x, Math.min(center.x, max.x));
    closestPoint.y = Math.max(min.y, Math.min(center.y, max.y));
    closestPoint.z = Math.max(min.z, Math.min(center.z, max.z));

    // 计算球体中心到最近点的距离
    const distance = new CANNON.Vec3();
    distance.copy(closestPoint);
    distance.vsub(center, distance);
    const distanceSquared = distance.lengthSquared();

    // 如果距离小于等于半径，则相交
    return distanceSquared <= radius * radius;
  }

  /**
   * 查询与物体可能碰撞的物体
   * @param body 物体
   * @returns 可能碰撞的物体
   */
  public queryPotentialCollisions(body: CANNON.Body): CANNON.Body[] {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return [];
    }

    // 获取物体占据的哈希值
    const hashes = this.bodyToHashes.get(body);
    if (!hashes) {
      return [];
    }

    // 收集单元内的所有物体
    const bodies = new Set<CANNON.Body>();
    for (const hash of hashes) {
      const cell = this.hashTable.get(hash);
      if (cell) {
        for (const otherBody of cell) {
          // 排除自身
          if (otherBody !== body) {
            bodies.add(otherBody);
          }
        }
      }
    }

    return Array.from(bodies);
  }

  /**
   * 获取所有物体
   * @returns 所有物体
   */
  public getBodies(): CANNON.Body[] {
    return Array.from(this.bodies);
  }

  /**
   * 获取物体数量
   * @returns 物体数量
   */
  public getBodyCount(): number {
    return this.bodies.size;
  }

  /**
   * 清空
   */
  public clear(): void {
    this.hashTable.clear();
    this.bodyToHashes.clear();
    this.bodies.clear();
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.clear();

    // 销毁调试网格
    if (this.debugMesh) {
      while (this.debugMesh.children.length > 0) {
        const child = this.debugMesh.children[0];
        this.debugMesh.remove(child);
        if (child instanceof THREE.GridHelper) {
          (child as any).dispose();
        }
      }
      this.debugMesh = null;
    }
  }

  /**
   * 获取调试信息
   * @returns 调试信息
   */
  public getDebugInfo(): any {
    return {
      cellSize: this.cellSize,
      worldSize: this.worldSize,
      worldCenter: this.worldCenter,
      useDynamicHash: this.useDynamicHash,
      cellCount: this.hashTable.size,
      bodyCount: this.bodies.size,
      averageBodiesPerCell: this.hashTable.size > 0 ? this.bodies.size / this.hashTable.size : 0
    };
  }

  /**
   * 获取调试网格
   * @returns 调试网格
   */
  public getDebugMesh(): THREE.Object3D {
    if (!this.debugMesh) {
      this.createDebugMesh();
    }
    return this.debugMesh!;
  }
}
