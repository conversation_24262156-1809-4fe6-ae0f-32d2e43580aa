/**
 * 物理系统空间分区
 * 用于加速碰撞检测
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';

/**
 * 空间分区策略枚举
 */
export enum SpatialPartitioningStrategy {
  /** 均匀网格 */
  UNIFORM_GRID = 'uniform_grid',
  /** 八叉树 */
  OCTREE = 'octree',
  /** BVH树 */
  BVH = 'bvh',
  /** 松散八叉树 */
  LOOSE_OCTREE = 'loose_octree',
  /** 空间哈希 */
  SPATIAL_HASH = 'spatial_hash'
}

/**
 * 空间分区接口
 */
export interface ISpatialPartitioning {
  /** 添加物体 */
  add(body: CANNON.Body): void;
  /** 移除物体 */
  remove(body: CANNON.Body): void;
  /** 更新物体 */
  update(body: CANNON.Body): void;
  /** 更新所有物体 */
  updateAll(): void;
  /** 查询区域内的物体 */
  queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[];
  /** 查询射线碰撞的物体 */
  queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[];
  /** 查询球体碰撞的物体 */
  querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[];
  /** 查询与物体可能碰撞的物体 */
  queryPotentialCollisions(body: CANNON.Body): CANNON.Body[];
  /** 获取所有物体 */
  getBodies(): CANNON.Body[];
  /** 获取物体数量 */
  getBodyCount(): number;
  /** 清空 */
  clear(): void;
  /** 销毁 */
  dispose(): void;
  /** 获取调试信息 */
  getDebugInfo(): any;
  /** 获取调试网格 */
  getDebugMesh(): THREE.Object3D;
}

/**
 * 碰撞对接口
 */
export interface CollisionPair {
  /** 物体A */
  bodyA: CANNON.Body;
  /** 物体B */
  bodyB: CANNON.Body;
}

/**
 * 空间分区工厂
 */
export class SpatialPartitioningFactory {
  /**
   * 创建空间分区
   * @param strategy 空间分区策略
   * @param options 空间分区选项
   * @returns 空间分区
   */
  public static create(strategy: SpatialPartitioningStrategy, options: any = {}): ISpatialPartitioning {
    switch (strategy) {
      case SpatialPartitioningStrategy.UNIFORM_GRID:
        return new UniformGrid(options);
      case SpatialPartitioningStrategy.OCTREE:
        // 需要动态导入 Octree 类
        const { Octree } = require('./Octree');
        return new Octree(options);
      case SpatialPartitioningStrategy.BVH:
        // BVH 树暂未实现，使用八叉树代替
        Debug.warn('SpatialPartitioningFactory', 'BVH树暂未实现，使用八叉树代替');
        const { Octree: OctreeBVH } = require('./Octree');
        return new OctreeBVH(options);
      case SpatialPartitioningStrategy.LOOSE_OCTREE:
        // 松散八叉树使用八叉树实现，设置松散因子
        Debug.warn('SpatialPartitioningFactory', '松散八叉树使用八叉树实现');
        const { Octree: OctreeLoose } = require('./Octree');
        return new OctreeLoose({ ...options, useLooseOctree: true });
      case SpatialPartitioningStrategy.SPATIAL_HASH:
        // 需要动态导入 SpatialHash 类
        const { SpatialHash } = require('./SpatialHash');
        return new SpatialHash(options);
      default:
        return new UniformGrid(options);
    }
  }
}

/**
 * 均匀网格配置接口
 */
export interface UniformGridOptions {
  /** 网格大小 */
  cellSize?: number;
  /** 世界大小 */
  worldSize?: number;
  /** 世界中心 */
  worldCenter?: CANNON.Vec3;
  /** 是否自动调整大小 */
  autoResize?: boolean;
  /** 是否使用动态网格 */
  useDynamicGrid?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 均匀网格类
 */
export class UniformGrid implements ISpatialPartitioning {
  /** 网格大小 */
  private cellSize: number;

  /** 世界大小 */
  private worldSize: number;

  /** 世界中心 */
  private worldCenter: CANNON.Vec3;

  /** 是否自动调整大小 */
  private autoResize: boolean;

  /** 是否使用动态网格 */
  private useDynamicGrid: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 网格单元数量 */
  private cellCount: number;

  /** 网格单元 */
  private cells: Map<string, Set<CANNON.Body>>;

  /** 物体到单元的映射 */
  private bodyToCells: Map<CANNON.Body, Set<string>>;

  /** 物体列表 */
  private bodies: Set<CANNON.Body>;

  /** 调试网格 */
  private debugMesh: THREE.Object3D | null = null;

  /**
   * 创建均匀网格
   * @param options 均匀网格配置
   */
  constructor(options: UniformGridOptions = {}) {
    this.cellSize = options.cellSize || 5;
    this.worldSize = options.worldSize || 100;
    this.worldCenter = options.worldCenter || new CANNON.Vec3(0, 0, 0);
    this.autoResize = options.autoResize !== undefined ? options.autoResize : true;
    this.useDynamicGrid = options.useDynamicGrid !== undefined ? options.useDynamicGrid : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    this.cellCount = Math.ceil(this.worldSize / this.cellSize);
    this.cells = new Map();
    this.bodyToCells = new Map();
    this.bodies = new Set();

    if (this.useDebugVisualization) {
      this.createDebugMesh();
    }
  }

  /**
   * 创建调试网格
   */
  private createDebugMesh(): void {
    this.debugMesh = new THREE.Object3D();
    this.debugMesh.name = 'uniform_grid_debug';

    // 创建网格线
    const gridHelper = new THREE.GridHelper(this.worldSize, this.cellCount);
    (gridHelper as any).setPosition(0, 0, 0);
    this.debugMesh.add(gridHelper);
  }

  /**
   * 获取单元索引
   * @param position 位置
   * @returns 单元索引
   */
  private getCellIndex(position: CANNON.Vec3): { x: number; y: number; z: number } {
    const halfWorldSize = this.worldSize / 2;
    const x = Math.floor((position.x + halfWorldSize - this.worldCenter.x) / this.cellSize);
    const y = Math.floor((position.y + halfWorldSize - this.worldCenter.y) / this.cellSize);
    const z = Math.floor((position.z + halfWorldSize - this.worldCenter.z) / this.cellSize);
    return { x, y, z };
  }

  /**
   * 获取单元键
   * @param x X索引
   * @param y Y索引
   * @param z Z索引
   * @returns 单元键
   */
  private getCellKey(x: number, y: number, z: number): string {
    return `${x},${y},${z}`;
  }

  /**
   * 获取物体占据的单元
   * @param body 物体
   * @returns 单元键列表
   */
  private getBodyCells(body: CANNON.Body): string[] {
    // 获取物体的AABB
    const aabb = this.getBodyAABB(body);
    const min = this.getCellIndex(aabb.lowerBound);
    const max = this.getCellIndex(aabb.upperBound);

    // 收集物体占据的所有单元
    const cells: string[] = [];
    for (let x = min.x; x <= max.x; x++) {
      for (let y = min.y; y <= max.y; y++) {
        for (let z = min.z; z <= max.z; z++) {
          cells.push(this.getCellKey(x, y, z));
        }
      }
    }

    return cells;
  }

  /**
   * 获取物体的AABB
   * @param body 物体
   * @returns AABB
   */
  private getBodyAABB(body: CANNON.Body): CANNON.AABB {
    const aabb = new CANNON.AABB();
    // 计算物体的AABB
    if (body.shapes.length > 0) {
      // 使用 calculateWorldAABB 方法计算世界空间的AABB
      body.shapes[0].calculateWorldAABB(body.position, body.quaternion, aabb.lowerBound, aabb.upperBound);
    } else {
      // 如果没有形状，使用物体位置作为点AABB
      aabb.lowerBound.copy(body.position);
      aabb.upperBound.copy(body.position);
    }
    return aabb;
  }

  /**
   * 添加物体
   * @param body 物体
   */
  public add(body: CANNON.Body): void {
    // 如果物体已经存在，则先移除
    if (this.bodies.has(body)) {
      this.remove(body);
    }

    // 添加物体
    this.bodies.add(body);

    // 获取物体占据的单元
    const cells = this.getBodyCells(body);
    const cellSet = new Set<string>(cells);
    this.bodyToCells.set(body, cellSet);

    // 将物体添加到单元
    for (const cellKey of cells) {
      let cell = this.cells.get(cellKey);
      if (!cell) {
        cell = new Set<CANNON.Body>();
        this.cells.set(cellKey, cell);
      }
      cell.add(body);
    }

    // 如果启用自动调整大小，则检查是否需要调整网格大小
    if (this.autoResize) {
      this.checkResize(body);
    }
  }

  /**
   * 检查是否需要调整网格大小
   * @param body 物体
   */
  private checkResize(body: CANNON.Body): void {
    // 获取物体的AABB
    const aabb = this.getBodyAABB(body);
    const halfWorldSize = this.worldSize / 2;

    // 检查物体是否超出世界范围
    const minX = this.worldCenter.x - halfWorldSize;
    const minY = this.worldCenter.y - halfWorldSize;
    const minZ = this.worldCenter.z - halfWorldSize;
    const maxX = this.worldCenter.x + halfWorldSize;
    const maxY = this.worldCenter.y + halfWorldSize;
    const maxZ = this.worldCenter.z + halfWorldSize;

    // 如果物体超出世界范围，则调整网格大小
    if (aabb.lowerBound.x < minX || aabb.lowerBound.y < minY || aabb.lowerBound.z < minZ ||
        aabb.upperBound.x > maxX || aabb.upperBound.y > maxY || aabb.upperBound.z > maxZ) {
      // 计算新的世界大小
      const newWorldSize = this.worldSize * 2;
      Debug.log('UniformGrid', `调整网格大小：${this.worldSize} -> ${newWorldSize}`);

      // 更新网格参数
      this.worldSize = newWorldSize;
      this.cellCount = Math.ceil(this.worldSize / this.cellSize);

      // 重新构建网格
      this.rebuild();

      // 更新调试网格
      if (this.useDebugVisualization && this.debugMesh) {
        this.updateDebugMesh();
      }
    }
  }

  /**
   * 更新调试网格
   */
  private updateDebugMesh(): void {
    if (!this.debugMesh) {
      return;
    }

    // 移除旧的网格线
    while (this.debugMesh.children.length > 0) {
      const child = this.debugMesh.children[0];
      this.debugMesh.remove(child);
      if (child instanceof THREE.GridHelper) {
        (child as any).dispose();
      }
    }

    // 创建新的网格线
    const gridHelper = new THREE.GridHelper(this.worldSize, this.cellCount);
    (gridHelper as any).setPosition(0, 0, 0);
    this.debugMesh.add(gridHelper);
  }
  /**
   * 重建网格
   */
  private rebuild(): void {
    // 清空网格
    this.cells.clear();
    this.bodyToCells.clear();

    // 重新添加所有物体
    for (const body of this.bodies) {
      // 获取物体占据的单元
      const cells = this.getBodyCells(body);
      const cellSet = new Set<string>(cells);
      this.bodyToCells.set(body, cellSet);

      // 将物体添加到单元
      for (const cellKey of cells) {
        let cell = this.cells.get(cellKey);
        if (!cell) {
          cell = new Set<CANNON.Body>();
          this.cells.set(cellKey, cell);
        }
        cell.add(body);
      }
    }
  }

  /**
   * 移除物体
   * @param body 物体
   */
  public remove(body: CANNON.Body): void {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return;
    }

    // 移除物体
    this.bodies.delete(body);

    // 获取物体占据的单元
    const cells = this.bodyToCells.get(body);
    if (cells) {
      // 从单元中移除物体
      for (const cellKey of cells) {
        const cell = this.cells.get(cellKey);
        if (cell) {
          cell.delete(body);
          // 如果单元为空，则移除单元
          if (cell.size === 0) {
            this.cells.delete(cellKey);
          }
        }
      }
    }

    // 移除物体到单元的映射
    this.bodyToCells.delete(body);
  }

  /**
   * 更新物体
   * @param body 物体
   */
  public update(body: CANNON.Body): void {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return;
    }

    // 获取物体当前占据的单元
    const oldCells = this.bodyToCells.get(body);
    if (!oldCells) {
      return;
    }

    // 获取物体新占据的单元
    const newCells = this.getBodyCells(body);
    const newCellSet = new Set<string>(newCells);

    // 如果单元没有变化，则直接返回
    if (this.areCellsEqual(oldCells, newCellSet)) {
      return;
    }

    // 从旧单元中移除物体
    for (const cellKey of oldCells) {
      // 如果新单元中也包含该单元，则跳过
      if (newCellSet.has(cellKey)) {
        continue;
      }

      // 从单元中移除物体
      const cell = this.cells.get(cellKey);
      if (cell) {
        cell.delete(body);
        // 如果单元为空，则移除单元
        if (cell.size === 0) {
          this.cells.delete(cellKey);
        }
      }
    }

    // 将物体添加到新单元
    for (const cellKey of newCellSet) {
      // 如果旧单元中也包含该单元，则跳过
      if (oldCells.has(cellKey)) {
        continue;
      }

      // 将物体添加到单元
      let cell = this.cells.get(cellKey);
      if (!cell) {
        cell = new Set<CANNON.Body>();
        this.cells.set(cellKey, cell);
      }
      cell.add(body);
    }

    // 更新物体到单元的映射
    this.bodyToCells.set(body, newCellSet);
  }

  /**
   * 比较两个单元集合是否相等
   * @param a 单元集合A
   * @param b 单元集合B
   * @returns 是否相等
   */
  private areCellsEqual(a: Set<string>, b: Set<string>): boolean {
    if (a.size !== b.size) {
      return false;
    }

    for (const cell of a) {
      if (!b.has(cell)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 更新所有物体
   */
  public updateAll(): void {
    for (const body of this.bodies) {
      this.update(body);
    }
  }

  /**
   * 查询区域内的物体
   * @param min 最小坐标
   * @param max 最大坐标
   * @returns 区域内的物体
   */
  public queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[] {
    // 获取区域占据的单元
    const minCell = this.getCellIndex(min);
    const maxCell = this.getCellIndex(max);

    // 收集区域内的所有物体
    const bodies = new Set<CANNON.Body>();
    for (let x = minCell.x; x <= maxCell.x; x++) {
      for (let y = minCell.y; y <= maxCell.y; y++) {
        for (let z = minCell.z; z <= maxCell.z; z++) {
          const cellKey = this.getCellKey(x, y, z);
          const cell = this.cells.get(cellKey);
          if (cell) {
            for (const body of cell) {
              bodies.add(body);
            }
          }
        }
      }
    }

    // 过滤出真正在区域内的物体
    return Array.from(bodies).filter(body => {
      const aabb = this.getBodyAABB(body);
      return this.aabbOverlap(aabb.lowerBound, aabb.upperBound, min, max);
    });
  }

  /**
   * 检查两个AABB是否重叠
   * @param minA AABB A的最小坐标
   * @param maxA AABB A的最大坐标
   * @param minB AABB B的最小坐标
   * @param maxB AABB B的最大坐标
   * @returns 是否重叠
   */
  private aabbOverlap(minA: CANNON.Vec3, maxA: CANNON.Vec3, minB: CANNON.Vec3, maxB: CANNON.Vec3): boolean {
    return (
      maxA.x >= minB.x && minA.x <= maxB.x &&
      maxA.y >= minB.y && minA.y <= maxB.y &&
      maxA.z >= minB.z && minA.z <= maxB.z
    );
  }

  /**
   * 查询射线碰撞的物体
   * @param from 射线起点
   * @param to 射线终点
   * @returns 射线碰撞的物体
   */
  public queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[] {
    // 创建射线
    const direction = new CANNON.Vec3();
    direction.copy(to);
    direction.vsub(from, direction);
    const length = direction.length();
    direction.normalize();

    // 使用DDA算法遍历射线经过的单元
    const bodies = new Set<CANNON.Body>();
    this.traverseRay(from, direction, length, (cellKey) => {
      const cell = this.cells.get(cellKey);
      if (cell) {
        for (const body of cell) {
          bodies.add(body);
        }
      }
    });

    // 过滤出真正与射线碰撞的物体
    return Array.from(bodies).filter(body => {
      // 在实际项目中，这里应该使用更精确的射线-物体碰撞检测
      // 这里使用简化的实现
      const aabb = this.getBodyAABB(body);
      return this.rayAabbIntersect(from, direction, length, aabb.lowerBound, aabb.upperBound);
    });
  }

  /**
   * 遍历射线经过的单元
   * @param from 射线起点
   * @param direction 射线方向
   * @param length 射线长度
   * @param callback 回调函数
   */
  private traverseRay(from: CANNON.Vec3, direction: CANNON.Vec3, length: number, callback: (cellKey: string) => void): void {
    // 获取起点所在的单元
    const startCell = this.getCellIndex(from);
    let x = startCell.x;
    let y = startCell.y;
    let z = startCell.z;

    // 调用起点单元的回调
    callback(this.getCellKey(x, y, z));

    // 计算射线与单元边界的交点
    const tDeltaX = Math.abs(direction.x) < 1e-6 ? 1e6 : Math.abs(this.cellSize / direction.x);
    const tDeltaY = Math.abs(direction.y) < 1e-6 ? 1e6 : Math.abs(this.cellSize / direction.y);
    const tDeltaZ = Math.abs(direction.z) < 1e-6 ? 1e6 : Math.abs(this.cellSize / direction.z);

    // 计算射线到下一个单元边界的距离
    const halfWorldSize = this.worldSize / 2;
    const cellX = (x * this.cellSize) - halfWorldSize + this.worldCenter.x;
    const cellY = (y * this.cellSize) - halfWorldSize + this.worldCenter.y;
    const cellZ = (z * this.cellSize) - halfWorldSize + this.worldCenter.z;

    let tMaxX = direction.x > 0 ?
      ((cellX + this.cellSize) - from.x) / direction.x :
      (cellX - from.x) / direction.x;
    let tMaxY = direction.y > 0 ?
      ((cellY + this.cellSize) - from.y) / direction.y :
      (cellY - from.y) / direction.y;
    let tMaxZ = direction.z > 0 ?
      ((cellZ + this.cellSize) - from.z) / direction.z :
      (cellZ - from.z) / direction.z;

    // 计算步进方向
    const stepX = direction.x > 0 ? 1 : -1;
    const stepY = direction.y > 0 ? 1 : -1;
    const stepZ = direction.z > 0 ? 1 : -1;

    // 遍历射线经过的单元
    let t = 0;
    while (t < length) {
      // 找到最近的交点
      if (tMaxX < tMaxY && tMaxX < tMaxZ) {
        t = tMaxX;
        tMaxX += tDeltaX;
        x += stepX;
      } else if (tMaxY < tMaxZ) {
        t = tMaxY;
        tMaxY += tDeltaY;
        y += stepY;
      } else {
        t = tMaxZ;
        tMaxZ += tDeltaZ;
        z += stepZ;
      }

      // 如果超出射线长度，则停止
      if (t > length) {
        break;
      }

      // 调用单元的回调
      callback(this.getCellKey(x, y, z));
    }
  }

  /**
   * 检查射线是否与AABB相交
   * @param from 射线起点
   * @param direction 射线方向
   * @param length 射线长度
   * @param min AABB的最小坐标
   * @param max AABB的最大坐标
   * @returns 是否相交
   */
  private rayAabbIntersect(from: CANNON.Vec3, direction: CANNON.Vec3, length: number, min: CANNON.Vec3, max: CANNON.Vec3): boolean {
    // 计算射线与AABB各面的交点
    const tmin = new CANNON.Vec3();
    const tmax = new CANNON.Vec3();

    tmin.x = (min.x - from.x) / direction.x;
    tmax.x = (max.x - from.x) / direction.x;
    if (tmin.x > tmax.x) {
      const temp = tmin.x;
      tmin.x = tmax.x;
      tmax.x = temp;
    }

    tmin.y = (min.y - from.y) / direction.y;
    tmax.y = (max.y - from.y) / direction.y;
    if (tmin.y > tmax.y) {
      const temp = tmin.y;
      tmin.y = tmax.y;
      tmax.y = temp;
    }

    tmin.z = (min.z - from.z) / direction.z;
    tmax.z = (max.z - from.z) / direction.z;
    if (tmin.z > tmax.z) {
      const temp = tmin.z;
      tmin.z = tmax.z;
      tmax.z = temp;
    }

    // 计算交点范围
    const tNear = Math.max(tmin.x, Math.max(tmin.y, tmin.z));
    const tFar = Math.min(tmax.x, Math.min(tmax.y, tmax.z));

    // 如果最近的交点比最远的交点还远，或者最远的交点为负，则没有相交
    if (tNear > tFar || tFar < 0) {
      return false;
    }

    // 如果最近的交点超出射线长度，则没有相交
    if (tNear > length) {
      return false;
    }

    return true;
  }

  /**
   * 查询球体碰撞的物体
   * @param center 球体中心
   * @param radius 球体半径
   * @returns 球体碰撞的物体
   */
  public querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[] {
    // 创建包围球体的AABB
    const min = new CANNON.Vec3(center.x - radius, center.y - radius, center.z - radius);
    const max = new CANNON.Vec3(center.x + radius, center.y + radius, center.z + radius);

    // 查询AABB内的物体
    const bodies = this.queryRegion(min, max);

    // 过滤出真正与球体碰撞的物体
    return bodies.filter(body => {
      // 在实际项目中，这里应该使用更精确的球体-物体碰撞检测
      // 这里使用简化的实现
      const aabb = this.getBodyAABB(body);
      return this.sphereAabbIntersect(center, radius, aabb.lowerBound, aabb.upperBound);
    });
  }

  /**
   * 检查球体是否与AABB相交
   * @param center 球体中心
   * @param radius 球体半径
   * @param min AABB的最小坐标
   * @param max AABB的最大坐标
   * @returns 是否相交
   */
  private sphereAabbIntersect(center: CANNON.Vec3, radius: number, min: CANNON.Vec3, max: CANNON.Vec3): boolean {
    // 计算球体中心到AABB的最近点
    const closestPoint = new CANNON.Vec3();
    closestPoint.x = Math.max(min.x, Math.min(center.x, max.x));
    closestPoint.y = Math.max(min.y, Math.min(center.y, max.y));
    closestPoint.z = Math.max(min.z, Math.min(center.z, max.z));

    // 计算球体中心到最近点的距离
    const distance = new CANNON.Vec3();
    distance.copy(closestPoint);
    distance.vsub(center, distance);
    const distanceSquared = distance.lengthSquared();

    // 如果距离小于等于半径，则相交
    return distanceSquared <= radius * radius;
  }

  /**
   * 查询与物体可能碰撞的物体
   * @param body 物体
   * @returns 可能碰撞的物体
   */
  public queryPotentialCollisions(body: CANNON.Body): CANNON.Body[] {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return [];
    }

    // 获取物体占据的单元
    const cells = this.bodyToCells.get(body);
    if (!cells) {
      return [];
    }

    // 收集单元内的所有物体
    const bodies = new Set<CANNON.Body>();
    for (const cellKey of cells) {
      const cell = this.cells.get(cellKey);
      if (cell) {
        for (const otherBody of cell) {
          // 排除自身
          if (otherBody !== body) {
            bodies.add(otherBody);
          }
        }
      }
    }

    return Array.from(bodies);
  }

  /**
   * 获取所有物体
   * @returns 所有物体
   */
  public getBodies(): CANNON.Body[] {
    return Array.from(this.bodies);
  }

  /**
   * 获取物体数量
   * @returns 物体数量
   */
  public getBodyCount(): number {
    return this.bodies.size;
  }

  /**
   * 清空
   */
  public clear(): void {
    this.cells.clear();
    this.bodyToCells.clear();
    this.bodies.clear();
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.clear();

    // 销毁调试网格
    if (this.debugMesh) {
      while (this.debugMesh.children.length > 0) {
        const child = this.debugMesh.children[0];
        this.debugMesh.remove(child);
        if (child instanceof THREE.GridHelper) {
          (child as any).dispose();
        }
      }
      this.debugMesh = null;
    }
  }

  /**
   * 获取调试信息
   * @returns 调试信息
   */
  public getDebugInfo(): any {
    return {
      cellSize: this.cellSize,
      worldSize: this.worldSize,
      worldCenter: this.worldCenter,
      cellCount: this.cellCount,
      cellCount3D: this.cellCount * this.cellCount * this.cellCount,
      activeCellCount: this.cells.size,
      bodyCount: this.bodies.size,
      averageBodiesPerCell: this.cells.size > 0 ? this.bodies.size / this.cells.size : 0
    };
  }

  /**
   * 获取调试网格
   * @returns 调试网格
   */
  public getDebugMesh(): THREE.Object3D {
    if (!this.debugMesh) {
      this.createDebugMesh();
    }
    return this.debugMesh!;
  }
}