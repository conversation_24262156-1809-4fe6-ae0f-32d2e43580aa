/**
 * 物理场景导出器
 * 用于导出物理场景数据
 */
import * as THREE from 'three';
import { Scene } from '../../scene/Scene';
import { PhysicsSystem } from '../PhysicsSystem';
import { PhysicsBodyComponent  } from '../components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../components/PhysicsColliderComponent';
import { PhysicsConstraintComponent } from '../components/PhysicsConstraintComponent';
import { PhysicsWorldComponent  } from '../components/PhysicsWorldComponent';

/**
 * 物理场景导出选项
 */
export interface PhysicsSceneExportOptions {
  /** 是否包含物理体 */
  includeBodies?: boolean;
  /** 是否包含碰撞器 */
  includeColliders?: boolean;
  /** 是否包含约束 */
  includeConstraints?: boolean;
  /** 是否包含物理世界 */
  includeWorld?: boolean;
  /** 是否包含材质 */
  includeMaterials?: boolean;
  /** 是否美化JSON输出 */
  prettyPrint?: boolean;
  /** 是否包含元数据 */
  includeMetadata?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 物理场景导出数据
 */
export interface PhysicsSceneExportData {
  /** 版本 */
  version: string;
  /** 元数据 */
  metadata?: Record<string, any>;
  /** 物理世界数据 */
  world?: any;
  /** 物理体数据 */
  bodies?: any[];
  /** 碰撞器数据 */
  colliders?: any[];
  /** 约束数据 */
  constraints?: any[];
  /** 材质数据 */
  materials?: any[];
}

/**
 * 物理场景导出器
 */
export class PhysicsSceneExporter {
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;

  /**
   * 创建物理场景导出器
   * @param physicsSystem 物理系统
   */
  constructor(physicsSystem: PhysicsSystem) {
    this.physicsSystem = physicsSystem;
  }

  /**
   * 导出场景
   * @param scene 场景
   * @param options 导出选项
   * @returns 导出数据
   */
  public export(scene: Scene, options: PhysicsSceneExportOptions = {}): PhysicsSceneExportData {
    // 默认选项
    const defaultOptions: PhysicsSceneExportOptions = {
      includeBodies: true,
      includeColliders: true,
      includeConstraints: true,
      includeWorld: true,
      includeMaterials: true,
      prettyPrint: false,
      includeMetadata: true
    };

    // 合并选项
    const exportOptions = { ...defaultOptions, ...options };

    // 创建导出数据
    const exportData: PhysicsSceneExportData = {
      version: '1.0'
    };

    // 添加元数据
    if (exportOptions.includeMetadata) {
      exportData.metadata = {
        type: 'PhysicsScene',
        generator: 'PhysicsSceneExporter',
        date: new Date().toISOString(),
        ...exportOptions.metadata
      };
    }

    // 导出物理世界
    if (exportOptions.includeWorld) {
      exportData.world = this.exportWorld(scene);
    }

    // 导出物理体
    if (exportOptions.includeBodies) {
      exportData.bodies = this.exportBodies(scene);
    }

    // 导出碰撞器
    if (exportOptions.includeColliders) {
      exportData.colliders = this.exportColliders(scene);
    }

    // 导出约束
    if (exportOptions.includeConstraints) {
      exportData.constraints = this.exportConstraints(scene);
    }

    // 导出材质
    if (exportOptions.includeMaterials) {
      exportData.materials = this.exportMaterials();
    }

    return exportData;
  }

  /**
   * 导出为JSON
   * @param scene 场景
   * @param options 导出选项
   * @returns JSON字符串
   */
  public exportToJSON(scene: Scene, options: PhysicsSceneExportOptions = {}): string {
    const exportData = this.export(scene, options);

    return JSON.stringify(exportData, (_key, value) => {
      // 处理THREE.Vector3和THREE.Quaternion
      if (value instanceof THREE.Vector3) {
        return { x: value.x, y: value.y, z: value.z, __type: 'Vector3' };
      } else if (value instanceof THREE.Quaternion) {
        return { x: value.x, y: value.y, z: value.z, w: value.w, __type: 'Quaternion' };
      }
      return value;
    }, options.prettyPrint ? 2 : undefined);
  }

  /**
   * 导出物理世界
   * @param scene 场景
   * @returns 物理世界数据
   */
  private exportWorld(scene: Scene): any {
    // 查找场景中的物理世界组件
    const worldEntity = scene.getEntities().find((entity) => entity.hasComponent(PhysicsWorldComponent.type));

    if (!worldEntity) {
      return null;
    }

    const worldComponent = worldEntity.getComponent<PhysicsWorldComponent>(PhysicsWorldComponent.type);
    if (!worldComponent) {
      return null;
    }

    // 导出物理世界数据
    return {
      entityId: worldEntity.id,
      gravity: worldComponent.getGravity(),
      allowSleep: worldComponent.getAllowSleep(),
      iterations: worldComponent.getIterations(),
      broadphase: worldComponent.getBroadphaseAlgorithm()
    };
  }

  /**
   * 导出物理体
   * @param scene 场景
   * @returns 物理体数据数组
   */
  private exportBodies(scene: Scene): any[] {
    const bodies: any[] = [];

    // 遍历场景中的所有实体
    for (const entity of scene.getEntities()) {
      const bodyComponent = entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);

      if (bodyComponent) {
        // 导出物理体数据
        bodies.push({
          entityId: entity.id,
          type: bodyComponent.bodyType,
          mass: bodyComponent.mass,
          position: bodyComponent.position,
          quaternion: bodyComponent.quaternion,
          linearDamping: bodyComponent.linearDamping,
          angularDamping: bodyComponent.angularDamping,
          allowSleep: bodyComponent.allowSleep,
          sleepSpeedLimit: bodyComponent.sleepSpeedLimit,
          sleepTimeLimit: bodyComponent.sleepTimeLimit,
          fixedRotation: bodyComponent.fixedRotation,
          material: bodyComponent.material?.name || 'default',
          collisionFilterGroup: bodyComponent.collisionFilterGroup,
          collisionFilterMask: bodyComponent.collisionFilterMask,
          autoUpdateTransform: bodyComponent.autoUpdateTransform
        });
      }
    }

    return bodies;
  }

  /**
   * 导出碰撞器
   * @param scene 场景
   * @returns 碰撞器数据数组
   */
  private exportColliders(scene: Scene): any[] {
    const colliders: any[] = [];

    // 遍历场景中的所有实体
    for (const entity of scene.getEntities()) {
      const colliderComponent = entity.getComponent<PhysicsColliderComponent>(PhysicsColliderComponent.type);

      if (colliderComponent) {
        // 导出碰撞器数据
        colliders.push({
          entityId: entity.id,
          type: (colliderComponent as any).colliderType,
          params: (colliderComponent as any).params,
          offset: (colliderComponent as any).offset,
          orientation: (colliderComponent as any).orientation,
          isTrigger: (colliderComponent as any).isTrigger
        });
      }
    }

    return colliders;
  }

  /**
   * 导出约束
   * @param scene 场景
   * @returns 约束数据数组
   */
  private exportConstraints(scene: Scene): any[] {
    const constraints: any[] = [];

    // 遍历场景中的所有实体
    for (const entity of scene.getEntities()) {
      const constraintComponent = entity.getComponent<PhysicsConstraintComponent>(PhysicsConstraintComponent.type);

      if (constraintComponent) {
        // 导出约束数据
        constraints.push({
          entityId: entity.id,
          type: (constraintComponent as any).constraintType,
          targetEntity: (constraintComponent as any).targetEntity?.id,
          collideConnected: (constraintComponent as any).collideConnected,
          pivotA: (constraintComponent as any).pivotA,
          pivotB: (constraintComponent as any).pivotB,
          axisA: (constraintComponent as any).axisA,
          axisB: (constraintComponent as any).axisB,
          maxForce: (constraintComponent as any).maxForce
        });
      }
    }

    return constraints;
  }

  /**
   * 导出材质
   * @returns 材质数据数组
   */
  private exportMaterials(): any[] {
    // 获取物理世界
    const world = this.physicsSystem.getPhysicsWorld();

    // 从PhysicsMaterialFactory获取所有材质
    const materialMap = new Map<string, any>();

    // 遍历物理世界中的所有接触材质
    for (const contactMaterial of (world as any).contactmaterials || []) {
      const materialA = contactMaterial.materials[0];
      const materialB = contactMaterial.materials[1];

      // 记录材质A
      if (materialA && materialA.name && !materialMap.has(materialA.name)) {
        materialMap.set(materialA.name, {
          name: materialA.name,
          contactMaterials: []
        });
      }

      // 记录材质B
      if (materialB && materialB.name && !materialMap.has(materialB.name)) {
        materialMap.set(materialB.name, {
          name: materialB.name,
          contactMaterials: []
        });
      }

      // 记录接触材质
      if (materialA && materialB) {
        const contactMaterialData = {
          materialA: materialA.name,
          materialB: materialB.name,
          friction: contactMaterial.friction,
          restitution: contactMaterial.restitution,
          contactEquationStiffness: contactMaterial.contactEquationStiffness,
          contactEquationRelaxation: contactMaterial.contactEquationRelaxation,
          frictionEquationStiffness: contactMaterial.frictionEquationStiffness,
          frictionEquationRelaxation: contactMaterial.frictionEquationRelaxation
        };

        // 添加到材质A的接触材质列表
        const materialAData = materialMap.get(materialA.name);
        if (materialAData) {
          materialAData.contactMaterials.push(contactMaterialData);
        }
      }
    }

    // 转换为数组
    return Array.from(materialMap.values());
  }
}
